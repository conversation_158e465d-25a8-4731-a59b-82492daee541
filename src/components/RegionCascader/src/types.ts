/**
 * 行政区划级别映射
 */
export interface RegionLevelMap {
  /** 省 */
  province?: string
  /** 市 */
  city?: string
  /** 县区 */
  xzqdm?: string
  /** 乡镇街道 */
  town?: string
  /** 社区村 */
  village?: string
  /** 小区 */
  community?: string
}

/**
 * 行政区划节点数据
 */
export interface RegionNode {
  /** 节点ID */
  value: string
  /** 节点名称 */
  label: string
  /** 是否为叶子节点 */
  leaf: boolean
  /** 子节点 */
  children?: RegionNode[]
  /** 原始区域数据 */
  id: string
  parentId: string
  name: string
  regionLevel: number
  code?: string
  fullName?: string
  [key: string]: any
}

/**
 * RegionCascader 组件属性
 */
export interface RegionCascaderProps {
  /** 绑定值 - 可以是单个Code或Code数组 */
  modelValue?: string | string[]
  /** 初始父级节点Code */
  rootParentCode?: string
  /** 是否返回多级结果 */
  returnMultiLevel?: boolean
  /** 是否只返回最后一级 */
  returnLastLevel?: boolean
  /** 占位提示文字 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否可搜索 */
  filterable?: boolean
  /** 尺寸 */
  size?: 'large' | 'default' | 'small'
  /** 级联选择器的宽度 */
  width?: string | number
  /** 自定义类名 */
  class?: string
  /** 自定义样式 */
  style?: string | object
}

/**
 * RegionCascader 组件事件
 */
export interface RegionCascaderEmits {
  /** 值变化事件 */
  'update:modelValue': [value: string | string[]]
  /** 选择变化事件 */
  'change': [value: string | string[], selectedData: RegionLevelMap | null]
  /** 节点展开事件 */
  'expand-change': [value: string[]]
  /** 失去焦点事件 */
  'blur': [event: FocusEvent]
  /** 获得焦点事件 */
  'focus': [event: FocusEvent]
  /** 可见性变化事件 */
  'visible-change': [visible: boolean]
  /** 移除标签事件（多选时） */
  'remove-tag': [value: string]
}
