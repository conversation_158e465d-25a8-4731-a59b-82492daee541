import { ref, onMounted } from 'vue'
import { generateUUID } from '@/utils'
import * as DictDataApi from '@/api/system/dict/dict.type'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'
import { selectRule } from '@/components/FormCreate/src/config/selectRule'
import { cloneDeep } from 'lodash-es'

/**
 * 字典选择器（支持"其他"选项）规则
 */
export const useDictSelectWithOtherRule = () => {
  const label = '字典选择器（其他）'
  const name = 'DictSelectWithOther'
  const rules = cloneDeep(selectRule)
  const dictOptions = ref<{ label: string; value: string }[]>([]) // 字典类型下拉数据
  
  onMounted(async () => {
    const data = await DictDataApi.getSimpleDictTypeList()
    if (!data || data.length === 0) {
      return
    }
    dictOptions.value =
      data?.map((item: DictDataApi.DictTypeVO) => ({
        label: item.name,
        value: item.type
      })) ?? []
  })
  
  return {
    icon: 'icon-doc-text',
    label,
    name,
    rule() {
      return {
        type: name,
        field: generateUUID(),
        title: label,
        info: '',
        $required: false,
        // 设置默认值
        value: {
          selected: '',
          otherText: '',
          selectType: 'select'
        },
        // 自定义验证规则
        validate: [
          {
            // 验证"其他"输入框
            validator: (rule: any, value: any, callback: Function) => {
              console.log('🔍 FormCreate 验证 DictSelectWithOther:', { rule, value })

              // 如果值不存在，跳过验证
              if (!value) {
                callback()
                return
              }

              // 检查是否有选择值
              if (!value.selected) {
                callback()
                return
              }

              // 获取组件配置中的 otherValue
              const otherValue = rule.otherValue || 99

              // 检查是否选择了"其他"选项
              let isOtherSelected = false
              if (Array.isArray(value.selected)) {
                // 多选情况 - 使用宽松比较处理类型差异
                isOtherSelected = value.selected.some((val: any) => val == otherValue)
              } else {
                // 单选情况 - 使用宽松比较处理类型差异
                isOtherSelected = value.selected == otherValue
              }

              // 如果选择了"其他"但没有填写内容，验证失败
              if (isOtherSelected && (!value.otherText || value.otherText.trim() === '')) {
                callback(new Error('请输入其他内容'))
                return
              }

              // 验证通过
              callback()
            },
            trigger: ['change', 'blur']
          }
        ]
      }
    },
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'select',
          field: 'dictType',
          title: '字典类型',
          value: '',
          options: dictOptions.value
        },
        {
          type: 'select',
          field: 'valueType',
          title: '字典值类型',
          value: 'str',
          options: [
            { label: '数字', value: 'int' },
            { label: '字符串', value: 'str' },
            { label: '布尔值', value: 'bool' }
          ]
        },
        {
          type: 'select',
          field: 'selectType',
          title: '选择器类型',
          value: 'select',
          options: [
            { label: '下拉框', value: 'select' },
            { label: '单选框', value: 'radio' },
            { label: '多选框', value: 'checkbox' }
          ]
        },
        {
          type: 'input',
          field: 'otherValue',
          title: '"其他"选项的值',
          value: '99',
          info: '当字典中存在此值且被选择时会显示"其他"输入框（不会自动添加此选项）'
        },
        {
          type: 'input',
          field: 'otherLabel',
          title: '"其他"选项的标签',
          value: '其他',
          info: '用于调试显示，实际显示的是字典中对应值的标签'
        },
        {
          type: 'input',
          field: 'otherInputPlaceholder',
          title: '"其他"输入框占位符',
          value: '请输入其他内容',
          info: '"其他"输入框的占位提示文本'
        },
        {
          type: 'switch',
          field: 'keepOtherTextOnUnselect',
          title: '取消选择时保留输入内容',
          value: false,
          info: '取消选择"其他"时是否保留输入框中的内容'
        },
        {
          type: 'switch',
          field: 'disabled',
          title: '禁用',
          value: false
        },
        {
          type: 'switch',
          field: 'readonly',
          title: '只读',
          value: false
        },
        // 根据选择器类型显示不同的配置项
        {
          type: 'switch',
          field: 'filterable',
          title: '是否可搜索',
          value: false,
          // 只有下拉框支持搜索
          control: [
            {
              value: 'select',
              condition: '!=',
              method: 'hidden',
              rule: ['filterable']
            }
          ]
        },
        {
          type: 'switch',
          field: 'multiple',
          title: '是否多选',
          value: false,
          // 只有下拉框支持多选
          control: [
            {
              value: 'select',
              condition: '!=',
              method: 'hidden',
              rule: ['multiple']
            }
          ]
        },
        {
          type: 'switch',
          field: 'clearable',
          title: '是否可以清空选项',
          value: true,
          // 只有下拉框支持清空
          control: [
            {
              value: 'select',
              condition: '!=',
              method: 'hidden',
              rule: ['clearable']
            }
          ]
        },
        {
          type: 'input',
          field: 'placeholder',
          title: '占位符',
          value: '请选择',
          // 只有下拉框有占位符
          control: [
            {
              value: 'select',
              condition: '!=',
              method: 'hidden',
              rule: ['placeholder']
            }
          ]
        }
      ])
    }
  }
}
