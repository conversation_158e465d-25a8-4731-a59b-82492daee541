<template>
  <div class="standard-selector-example">
    <ContentWrap>
      <div class="layout-container">
        <!-- 左侧：标准选择器 -->
        <div class="left-panel">
          <div class="panel-header">
            <h3>标准体系</h3>
            <el-button 
              size="small" 
              @click="refreshStandards"
              :loading="refreshing"
            >
              <Icon icon="ep:refresh" class="mr-5px" />
              刷新
            </el-button>
          </div>
          
          <StandardSelector
            ref="standardSelectorRef"
            :show-search="true"
            :show-pagination="true"
            :show-status="false"
            :table-height="500"
            :page-size="20"
            :enabled-only="true"
            @select="handleStandardSelect"
            @change="handleStandardChange"
          />
        </div>
        
        <!-- 右侧：相关数据列表 -->
        <div class="right-panel">
          <div class="panel-header">
            <h3>
              {{ selectedStandard ? `${selectedStandard.name} - 指标项` : '请选择标准体系' }}
            </h3>
            <div class="header-actions" v-if="selectedStandard">
              <el-button 
                type="primary" 
                size="small"
                @click="handleAddIndicator"
              >
                <Icon icon="ep:plus" class="mr-5px" />
                添加指标项
              </el-button>
            </div>
          </div>
          
          <div class="content-area">
            <div v-if="!selectedStandard" class="empty-state">
              <Icon icon="ep:document" size="48" color="#c0c4cc" />
              <p>请从左侧选择一个标准体系</p>
            </div>
            
            <div v-else class="indicator-list">
              <!-- 这里可以放置指标项列表组件 -->
              <el-alert
                title="指标项列表"
                :description="`当前选中标准：${selectedStandard.name}（ID: ${selectedStandard.id}）`"
                type="info"
                show-icon
                :closable="false"
              />
              
              <!-- 模拟指标项列表 -->
              <el-table 
                :data="mockIndicators" 
                style="margin-top: 16px;"
                v-loading="indicatorLoading"
              >
                <el-table-column label="指标名称" prop="name" />
                <el-table-column label="指标类型" prop="type" width="100" />
                <el-table-column label="单位" prop="unit" width="80" />
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button link type="primary" size="small">
                      编辑
                    </el-button>
                    <el-button link type="danger" size="small">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { StandardSelector } from '../index'
import type { CitystandardVO } from '@/api/urban/citystandard'

defineOptions({ name: 'StandardSelectorExample' })

// 响应式数据
const standardSelectorRef = ref()
const selectedStandard = ref<CitystandardVO | null>(null)
const refreshing = ref(false)
const indicatorLoading = ref(false)

// 模拟指标项数据
const mockIndicators = ref([
  { id: 1, name: '人口密度', type: '数值型', unit: '人/km²' },
  { id: 2, name: '绿化覆盖率', type: '百分比', unit: '%' },
  { id: 3, name: '交通便利度', type: '评分型', unit: '分' }
])

// 方法
/** 处理标准选择 */
const handleStandardSelect = (standard: CitystandardVO) => {
  console.log('🎯 选择标准:', standard)
  selectedStandard.value = standard
  
  // 模拟加载指标项数据
  loadIndicators(standard.id!)
}

/** 处理标准选择变化 */
const handleStandardChange = (standard: CitystandardVO | null) => {
  if (!standard) {
    selectedStandard.value = null
    mockIndicators.value = []
  }
}

/** 刷新标准列表 */
const refreshStandards = async () => {
  refreshing.value = true
  try {
    await standardSelectorRef.value?.refresh()
  } finally {
    refreshing.value = false
  }
}

/** 加载指标项数据 */
const loadIndicators = async (standardId: number) => {
  indicatorLoading.value = true
  try {
    // 这里应该调用实际的指标项API
    // const data = await IndicatorApi.getIndicatorsByStandardId(standardId)
    // mockIndicators.value = data
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    console.log('📊 加载标准ID为', standardId, '的指标项')
  } finally {
    indicatorLoading.value = false
  }
}

/** 添加指标项 */
const handleAddIndicator = () => {
  if (selectedStandard.value) {
    console.log('➕ 为标准', selectedStandard.value.name, '添加指标项')
    // 这里可以打开添加指标项的弹窗
  }
}

// 暴露方法供外部调用
defineExpose({
  getSelectedStandard: () => selectedStandard.value,
  selectStandardById: (id: number) => standardSelectorRef.value?.selectById(id),
  clearSelection: () => {
    standardSelectorRef.value?.clearSelection()
    selectedStandard.value = null
  }
})
</script>

<style lang="scss" scoped>
.standard-selector-example {
  height: 100%;
  
  .layout-container {
    display: flex;
    height: 600px;
    gap: 16px;
    
    .left-panel {
      width: 400px;
      display: flex;
      flex-direction: column;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      overflow: hidden;
    }
    
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      overflow: hidden;
    }
    
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .content-area {
      flex: 1;
      padding: 16px;
      overflow: auto;

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;
        
        p {
          margin-top: 16px;
          font-size: 14px;
        }
      }
      
      .indicator-list {
        height: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .layout-container {
    flex-direction: column;
    height: auto;
    
    .left-panel {
      width: 100%;
      height: 400px;
    }
    
    .right-panel {
      height: 400px;
    }
  }
}
</style>
