import type { CitystandardVO } from '@/api/urban/citystandard'

/**
 * 标准选择器组件属性
 */
export interface StandardSelectorProps {
  /** 是否显示搜索区域 */
  showSearch?: boolean
  /** 是否显示分页 */
  showPagination?: boolean
  /** 是否显示状态列 */
  showStatus?: boolean
  /** 表格高度 */
  tableHeight?: string | number
  /** 每页显示数量 */
  pageSize?: number
  /** 是否只显示启用的标准 */
  enabledOnly?: boolean
}

/**
 * 标准选择器组件事件
 */
export interface StandardSelectorEmits {
  /** 选择标准时触发 */
  select: [standard: CitystandardVO]
  /** 选择变化时触发（包括取消选择） */
  change: [standard: CitystandardVO | null]
}

/**
 * 标准选择器组件暴露的方法
 */
export interface StandardSelectorExpose {
  /** 获取当前选中的标准 */
  getSelectedStandard: () => CitystandardVO | null
  /** 清除选择 */
  clearSelection: () => void
  /** 刷新列表 */
  refresh: () => void
  /** 根据ID选中标准 */
  selectById: (id: number) => void
  /** 获取列表数据 */
  getList: () => Promise<void>
}

/**
 * 查询参数
 */
export interface StandardQueryParams {
  pageNo: number
  pageSize: number
  name?: string
  publishYear?: string
  enabled?: number
}
