import request from '@/config/axios'

// 行政区划 VO
export interface RegionVO {
  id: string // 主键
  parentId: string // 父级id
  name: string // 地区名称
  fullName: string // 地区全名
  status: number // 状态
  regionSeq: string // 序列
  code: string // 地区编号
  creater: string // 创建人
  sortNo: number // 排列顺序-从0开始, 数值越小越前
  regionLevel: number // 层级
  rankName: string // 层级名称
  geom?: string // 空间范围，WKT格式
}

// 行政区划 API
export const RegionApi = {
  // 查询行政区划列表
  getRegionList: async (params: { parentId?: string; parentCode?: string; name?: string; code?: string }) => {
    return await request.get({ url: `/urban/region/list`, params })
  },

  // 查询行政区划详情
  getRegion: async (id: string) => {
    return await request.get({ url: `/urban/region/get?id=` + id })
  },

  // 根据 code 查询行政区划详情
  getRegionByCode: async (code: string) => {
    return await request.get({ url: `/urban/region/getByCode?code=` + code })
  },

  // 新增行政区划
  createRegion: async (data: Omit<RegionVO, 'id' | 'regionSeq' | 'creater'>) => {
    return await request.post({ url: `/urban/region/create`, data })
  },

  // 修改行政区划
  updateRegion: async (data: Omit<RegionVO, 'regionSeq' | 'creater'>) => {
    return await request.put({ url: `/urban/region/update`, data })
  },

  // 删除行政区划
  deleteRegion: async (id: string) => {
    return await request.delete({ url: `/urban/region/delete?id=` + id })
  },

  // 导出行政区划 Excel
  exportRegion: async (params: { parentId?: string; name?: string; code?: string }) => {
    return await request.download({ url: `/urban/region/export-excel`, params })
  },
}
