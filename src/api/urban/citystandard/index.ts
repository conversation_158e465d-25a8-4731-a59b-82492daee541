import request from '@/config/axios'

// 城市标准体体系 VO
export interface CitystandardVO {
  id: number // 主键ID
  name: string // 名称
  publishYear: number // 发布年份
  category: string // 分类
  enabled: number // 是否启用
}

// 城市标准体体系 API
export const CitystandardApi = {
  // 查询城市标准体体系分页
  getCitystandardPage: async (params: any) => {
    return await request.get({ url: `/urban/citystandard/page`, params })
  },

  // 查询城市标准体体系详情
  getCitystandard: async (id: number) => {
    return await request.get({ url: `/urban/citystandard/get?id=` + id })
  },

  // 新增城市标准体体系
  createCitystandard: async (data: CitystandardVO) => {
    return await request.post({ url: `/urban/citystandard/create`, data })
  },

  // 修改城市标准体体系
  updateCitystandard: async (data: CitystandardVO) => {
    return await request.put({ url: `/urban/citystandard/update`, data })
  },

  // 删除城市标准体体系
  deleteCitystandard: async (id: number) => {
    return await request.delete({ url: `/urban/citystandard/delete?id=` + id })
  },

  // 导出城市标准体体系 Excel
  exportCitystandard: async (params) => {
    return await request.download({ url: `/urban/citystandard/export-excel`, params })
  },

  // 查询城市标准体体系简化列表（用于下拉选择）
  getCitystandardList: async (params: any) => {
    return await request.get({ url: `/urban/citystandard/list`, params })
  }
}