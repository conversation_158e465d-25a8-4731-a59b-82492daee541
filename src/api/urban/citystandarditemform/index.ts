import request from '@/config/axios'

// 指标目标采集表单 VO
export interface CitystandardItemFormVO {
  id: number // 主键ID
  itemId: number // 指标ID
  citystandardId: number // 城市指标体系ID
  formRule: object // 表单组件配置
  formOption: object // 表单全局配置
  remark: string // 备注
  formVersion: number // 版本
}

// 指标目标采集表单 API
export const CitystandardItemFormApi = {
  // 查询指标目标采集表单分页
  getCitystandardItemFormPage: async (params: any) => {
    return await request.get({ url: `/urban/citystandard-item-form/page`, params })
  },
  // 查询指标目标采集表单列表
  getCitystandardItemFormList: async (params: any) => {
    return await request.get({ url: `/urban/citystandard-item-form/list`, params })
  },

  // 查询指标目标采集表单详情
  getCitystandardItemForm: async (id: number) => {
    return await request.get({ url: `/urban/citystandard-item-form/get?id=` + id })
  },

  // 新增指标目标采集表单
  createCitystandardItemForm: async (data: CitystandardItemFormVO) => {
    return await request.post({ url: `/urban/citystandard-item-form/create`, data })
  },

  // 修改指标目标采集表单
  updateCitystandardItemForm: async (data: CitystandardItemFormVO) => {
    return await request.put({ url: `/urban/citystandard-item-form/update`, data })
  },

  // 删除指标目标采集表单
  deleteCitystandardItemForm: async (id: number) => {
    return await request.delete({ url: `/urban/citystandard-item-form/delete?id=` + id })
  },

  // 导出指标目标采集表单 Excel
  exportCitystandardItemForm: async (params) => {
    return await request.download({ url: `/urban/citystandard-item-form/export-excel`, params })
  }
}
