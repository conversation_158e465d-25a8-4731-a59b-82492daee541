<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="指标ID" prop="itemId">
        <el-input v-model="formData.itemId" placeholder="请输入指标ID" />
      </el-form-item>
      <el-form-item label="城市指标体系ID" prop="citystandardId">
        <el-input v-model="formData.citystandardId" placeholder="请输入城市指标体系ID" />
      </el-form-item>
      <el-form-item label="表单组件配置" prop="formRule">
        <el-input v-model="formData.formRule" placeholder="请输入表单组件配置" />
      </el-form-item>
      <el-form-item label="表单全局配置" prop="formOption">
        <el-input v-model="formData.formOption" placeholder="请输入表单全局配置" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="版本" prop="formVersion">
        <el-input v-model="formData.formVersion" placeholder="请输入版本" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CitystandardItemFormApi, CitystandardItemFormVO } from '@/api/urban/citystandarditemform'

/** 指标目标采集表单 表单 */
defineOptions({ name: 'CitystandardItemFormForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  itemId: undefined,
  citystandardId: undefined,
  formRule: undefined,
  formOption: undefined,
  remark: undefined,
  formVersion: undefined
})
const formRules = reactive({
  itemId: [{ required: true, message: '指标ID不能为空', trigger: 'blur' }],
  citystandardId: [{ required: true, message: '城市指标体系ID不能为空', trigger: 'blur' }],
  formRule: [{ required: true, message: '表单组件配置不能为空', trigger: 'blur' }],
  formOption: [{ required: true, message: '表单全局配置不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CitystandardItemFormApi.getCitystandardItemForm(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CitystandardItemFormVO
    if (formType.value === 'create') {
      await CitystandardItemFormApi.createCitystandardItemForm(data)
      message.success(t('common.createSuccess'))
    } else {
      await CitystandardItemFormApi.updateCitystandardItemForm(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    itemId: undefined,
    citystandardId: undefined,
    formRule: undefined,
    formOption: undefined,
    remark: undefined,
    formVersion: undefined
  }
  formRef.value?.resetFields()
}
</script>