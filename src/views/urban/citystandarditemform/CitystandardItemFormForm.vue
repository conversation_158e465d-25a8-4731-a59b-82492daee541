<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1500px" :close-on-click-modal="false">
    <div class="form-layout-container">
      <!-- 左侧：基本信息表单 -->
      <div class="left-form-panel">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          v-loading="formLoading"
        >
          <el-form-item label="城市指标体系" prop="citystandardId">
            <el-select
              v-model="formData.citystandardId"
              placeholder="请选择城市指标体系"
              clearable
              filterable
              style="width: 100%"
              @change="handleStandardChange"
            >
              <el-option
                v-for="item in citystandardList"
                :key="item.id"
                :label="`${item.name} (${item.publishYear})`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="指标项" prop="itemId">
            <el-select
              v-model="formData.itemId"
              placeholder="请选择指标项"
              clearable
              filterable
              style="width: 100%"
              :disabled="!formData.citystandardId"
            >
              <el-option
                v-for="item in citystandardItemList"
                :key="item.id"
                :label="`${item.itemName} (${item.itemCode})`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              placeholder="请输入备注"
              type="textarea"
              :rows="3"
            />
          </el-form-item>

          <el-form-item label="版本" prop="formVersion">
            <el-input-number
              v-model="formData.formVersion"
              placeholder="请输入版本"
              :min="1"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 表单配置状态显示 -->
          <el-divider content-position="left">配置状态</el-divider>

          <el-form-item label="表单组件配置">
            <el-tag :type="hasFormConfig ? 'success' : 'info'">
              {{ hasFormConfig ? '已配置' : '未配置' }}
            </el-tag>
          </el-form-item>

          <el-form-item label="表单全局配置">
            <el-tag :type="hasOptionConfig ? 'success' : 'info'">
              {{ hasOptionConfig ? '已配置' : '未配置' }}
            </el-tag>
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧：表单配置区域 -->
      <div class="right-config-panel">
        <el-tabs v-model="activeTab" type="card" class="config-tabs">
          <el-tab-pane label="表单组件配置" name="formRule">
            <div class="form-config-container">
              <div class="config-toolbar">
                <el-button-group>
                  <el-button
                    :type="designMode === 'design' ? 'primary' : ''"
                    @click="designMode = 'design'"
                    size="small"
                  >
                    <Icon icon="ep:edit" class="mr-5px" />
                    设计模式
                  </el-button>
                  <el-button
                    :type="designMode === 'preview' ? 'primary' : ''"
                    @click="designMode = 'preview'"
                    :disabled="!hasFormConfig"
                    size="small"
                  >
                    <Icon icon="ep:view" class="mr-5px" />
                    预览模式
                  </el-button>
                </el-button-group>
              </div>

              <!-- 设计器模式 -->
              <div v-show="designMode === 'design'" class="designer-container">
                <FcDesigner ref="designerRef" height="450px" />
              </div>

              <!-- 预览模式 -->
              <div v-show="designMode === 'preview'" class="preview-container">
                <form-create
                  v-if="hasFormConfig"
                  v-model="previewData.formData"
                  :rule="previewData.rule"
                  :option="previewData.option"
                />
                <div v-else class="no-config-tip">
                  <Icon icon="ep:warning" size="48" color="#e6a23c" />
                  <p>暂无表单配置，请先在设计模式中设计表单</p>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="表单全局配置" name="formOption">
            <div class="form-config-container">
              <div class="config-toolbar">
                <el-button-group>
                  <el-button
                    :type="optionDesignMode === 'design' ? 'primary' : ''"
                    @click="optionDesignMode = 'design'"
                    size="small"
                  >
                    <Icon icon="ep:edit" class="mr-5px" />
                    设计模式
                  </el-button>
                  <el-button
                    :type="optionDesignMode === 'preview' ? 'primary' : ''"
                    @click="optionDesignMode = 'preview'"
                    :disabled="!hasOptionConfig"
                    size="small"
                  >
                    <Icon icon="ep:view" class="mr-5px" />
                    预览模式
                  </el-button>
                </el-button-group>
              </div>

              <!-- 全局配置编辑器 -->
              <div v-show="optionDesignMode === 'design'" class="option-editor">
                <el-input
                  v-model="formOptionText"
                  type="textarea"
                  :rows="12"
                  placeholder="请输入表单全局配置JSON"
                  @blur="validateFormOption"
                />
                <div class="option-tips">
                  <el-alert
                    title="配置说明"
                    type="info"
                    :closable="false"
                    show-icon
                    size="small"
                  >
                    <template #default>
                      <p>表单全局配置用于设置表单的整体行为，如提交按钮、重置按钮等。</p>
                      <p>示例：{"submitBtn": false, "resetBtn": false}</p>
                    </template>
                  </el-alert>
                </div>
              </div>

              <!-- 配置预览 -->
              <div v-show="optionDesignMode === 'preview'" class="option-preview">
                <div v-if="hasOptionConfig" class="config-display">
                  <pre>{{ JSON.stringify(formData.formOption, null, 2) }}</pre>
                </div>
                <div v-else class="no-config-tip">
                  <Icon icon="ep:warning" size="48" color="#e6a23c" />
                  <p>暂无全局配置，请先在设计模式中配置</p>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CitystandardItemFormApi, CitystandardItemFormVO } from '@/api/urban/citystandarditemform'
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import FcDesigner from '@form-create/designer'
import { encodeConf, encodeFields, setConfAndFields, setConfAndFields2 } from '@/utils/formCreate'
import { useFormCreateDesigner } from '@/components/FormCreate'

/** 指标目标采集表单 表单 */
defineOptions({ name: 'CitystandardItemFormForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const activeTab = ref('formRule') // 当前激活的标签页
const designMode = ref('design') // 设计模式：design | preview
const optionDesignMode = ref('design') // 全局配置设计模式：design | preview

// 下拉选择数据
const citystandardList = ref<CitystandardVO[]>([]) // 城市指标体系列表
const citystandardItemList = ref<CitystandardItemVO[]>([]) // 指标项列表

// 设计器相关
const designerRef = ref() // 设计器引用
const formOptionText = ref('') // 表单全局配置文本

// 表单数据
const formData = ref({
  id: undefined,
  itemId: undefined,
  citystandardId: undefined,
  formRule: undefined,
  formOption: undefined,
  remark: undefined,
  formVersion: 1
})

// 预览数据
const previewData = ref({
  formData: {},
  rule: [],
  option: {
    submitBtn: false,
    resetBtn: false
  }
})

const formRules = reactive({
  itemId: [{ required: true, message: '指标项不能为空', trigger: 'change' }],
  citystandardId: [{ required: true, message: '城市指标体系不能为空', trigger: 'change' }],
  formRule: [{ required: true, message: '表单组件配置不能为空', trigger: 'blur' }],
  formOption: [{ required: true, message: '表单全局配置不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

// 计算属性
const hasFormConfig = computed(() => {
  return previewData.value.rule && previewData.value.rule.length > 0
})

const hasOptionConfig = computed(() => {
  return formData.value.formOption && Object.keys(formData.value.formOption).length > 0
})

// 初始化设计器
const initDesigner = async () => {
  if (designerRef.value) {
    await useFormCreateDesigner(designerRef)
  }
}

// 监听设计模式变化，更新预览数据
watch(designMode, (newMode) => {
  if (newMode === 'preview' && designerRef.value) {
    updatePreviewData()
  }
})

// 更新预览数据
const updatePreviewData = () => {
  if (designerRef.value) {
    try {
      const rule = JSON.parse(designerRef.value.getJson())
      const option = designerRef.value.getOption()

      previewData.value.rule = rule
      previewData.value.option = {
        ...option,
        submitBtn: false,
        resetBtn: false
      }

      // 更新表单数据
      formData.value.formRule = rule
    } catch (error) {
      console.error('更新预览数据失败:', error)
      message.error('表单配置格式错误')
    }
  }
}

// 验证表单全局配置
const validateFormOption = () => {
  if (formOptionText.value.trim()) {
    try {
      const option = JSON.parse(formOptionText.value)
      formData.value.formOption = option
    } catch (error) {
      message.error('JSON格式错误，请检查配置')
      formOptionText.value = JSON.stringify(formData.value.formOption || {}, null, 2)
    }
  } else {
    formData.value.formOption = {}
  }
}

// 加载城市指标体系列表
const loadCitystandardList = async () => {
  try {
    const data = await CitystandardApi.getCitystandardList({
      enabled: 1 // 只加载启用的
    })
    citystandardList.value = data
  } catch (error) {
    console.error('加载城市指标体系列表失败:', error)
  }
}

// 加载指标项列表
const loadCitystandardItemList = async (citystandardId: number) => {
  if (!citystandardId) {
    citystandardItemList.value = []
    return
  }

  try {
    const data = await CitystandardItemApi.getCitystandardItemList({
      citystandardId: citystandardId
    })
    citystandardItemList.value = data
  } catch (error) {
    console.error('加载指标项列表失败:', error)
  }
}

// 处理指标体系变化
const handleStandardChange = (citystandardId: number) => {
  formData.value.itemId = undefined // 清空指标项选择
  loadCitystandardItemList(citystandardId)
}

/** 打开弹窗 */
const open = async (type: string, id?: number, standardId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载基础数据
  await loadCitystandardList()

  // 如果传入了标准体系ID，设置默认值
  if (standardId && type === 'create') {
    formData.value.citystandardId = standardId
    await loadCitystandardItemList(standardId)
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await CitystandardItemFormApi.getCitystandardItemForm(id)
      formData.value = data

      // 加载对应的指标项列表
      if (data.citystandardId) {
        await loadCitystandardItemList(data.citystandardId)
      }

      // 设置设计器数据
      if (data.formRule && designerRef.value) {
        try {
          const rule = Array.isArray(data.formRule) ? data.formRule : JSON.parse(data.formRule)
          const option = data.formOption || {}
          setConfAndFields(designerRef, JSON.stringify(option), rule.map(item => JSON.stringify(item)))
        } catch (error) {
          console.error('设置设计器数据失败:', error)
        }
      }

      // 设置全局配置文本
      formOptionText.value = JSON.stringify(data.formOption || {}, null, 2)

    } finally {
      formLoading.value = false
    }
  }

  // 初始化设计器
  nextTick(() => {
    initDesigner()
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 更新表单配置数据
  if (designerRef.value) {
    updatePreviewData()
  }

  // 验证全局配置
  validateFormOption()

  // 提交请求
  formLoading.value = true
  try {
    const data = {
      ...formData.value,
      formRule: formData.value.formRule,
      formOption: formData.value.formOption
    } as unknown as CitystandardItemFormVO

    if (formType.value === 'create') {
      await CitystandardItemFormApi.createCitystandardItemForm(data)
      message.success(t('common.createSuccess'))
    } else {
      await CitystandardItemFormApi.updateCitystandardItemForm(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    itemId: undefined,
    citystandardId: undefined,
    formRule: undefined,
    formOption: undefined,
    remark: undefined,
    formVersion: 1
  }

  // 重置其他状态
  activeTab.value = 'formRule'
  designMode.value = 'design'
  optionDesignMode.value = 'design'
  formOptionText.value = ''
  citystandardItemList.value = []

  // 重置预览数据
  previewData.value = {
    formData: {},
    rule: [],
    option: {
      submitBtn: false,
      resetBtn: false
    }
  }

  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-layout-container {
  display: flex;
  gap: 20px;
  height: 600px;

  .left-form-panel {
    width: 350px;
    flex-shrink: 0;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 20px;
    background: #fafafa;
    overflow-y: auto;

    .el-form {
      height: 100%;
    }

    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .right-config-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;

    .config-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      :deep(.el-tabs__header) {
        margin: 0;
        background: #f8f9fa;
        padding: 0 16px;
        border-bottom: 1px solid #e4e7ed;
      }

      :deep(.el-tabs__content) {
        flex: 1;
        padding: 0;
        overflow: hidden;
      }

      :deep(.el-tab-pane) {
        height: 100%;
        padding: 0;
      }
    }
  }
}

.form-config-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .config-toolbar {
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .designer-container {
    flex: 1;
    padding: 16px;
    overflow: hidden;
  }

  .preview-container {
    flex: 1;
    padding: 16px;
    background: #fafafa;
    overflow-y: auto;
  }

  .option-editor {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;

    .el-textarea {
      flex: 1;
    }

    .option-tips {
      margin-top: 16px;
      flex-shrink: 0;
    }
  }

  .option-preview {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .config-display {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      height: 100%;

      pre {
        margin: 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #333;
      }
    }
  }

  .no-config-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;

    p {
      margin: 16px 0 0 0;
      font-size: 14px;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
  height: 600px;
  overflow: hidden;
}

// 响应式设计
@media (max-width: 1200px) {
  .form-layout-container {
    flex-direction: column;
    height: auto;

    .left-form-panel {
      width: 100%;
      height: auto;
      max-height: 300px;
    }

    .right-config-panel {
      height: 400px;
    }
  }
}
</style>
